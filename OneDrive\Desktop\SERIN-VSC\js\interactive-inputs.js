/**
 * نظام حقول الإدخال التفاعلية المتطورة
 * يوفر حقول إدخال أنيقة ومتفاعلة بدلاً من prompt() التقليدي
 */

class InteractiveInputSystem {
    constructor() {
        this.activeInput = null;
        this.inputId = 0;
        this.init();
    }

    init() {
        // إضافة أنماط CSS للحقول التفاعلية
        this.addStyles();
    }

    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* استخدام نفس الخطوط المستخدمة في الموقع */
            .interactive-input-dialog,
            .interactive-input-dialog * {
                font-family: 'Inter', 'Tajawal', sans-serif;
            }
            .interactive-input-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(4px);
                z-index: 20000;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .interactive-input-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .interactive-input-dialog {
                background: var(--bg-medium);
                border: 1px solid var(--border-color);
                border-radius: var(--radius-lg);
                padding: 20px;
                min-width: 360px;
                max-width: 450px;
                width: 90%;
                box-shadow: var(--shadow-lg);
                transform: scale(0.9) translateY(20px);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                direction: rtl;
            }

            .interactive-input-overlay.show .interactive-input-dialog {
                transform: scale(1) translateY(0);
            }

            .interactive-input-header {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 20px;
                padding-bottom: 16px;
                border-bottom: 1px solid var(--border-color);
            }

            .interactive-input-icon {
                width: 28px;
                height: 28px;
                border-radius: var(--radius-sm);
                background: var(--primary-color);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: white;
                box-shadow: var(--shadow-sm);
            }

            .interactive-input-title {
                font-size: 16px;
                font-weight: 600;
                color: var(--text-light);
                flex: 1;
            }

            .interactive-input-close {
                background: none;
                border: none;
                color: var(--text-medium);
                cursor: pointer;
                padding: 6px;
                border-radius: var(--radius-sm);
                transition: all var(--transition-fast);
                font-size: 16px;
                line-height: 1;
            }

            .interactive-input-close:hover {
                background: var(--bg-light);
                color: var(--text-light);
            }

            .interactive-input-content {
                margin-bottom: 20px;
            }

            .interactive-input-label {
                display: block;
                color: var(--text-medium);
                font-size: 13px;
                margin-bottom: 6px;
                font-weight: 500;
            }

            .interactive-input-field {
                width: 100%;
                padding: 10px 14px;
                border: 1px solid var(--border-color);
                border-radius: var(--radius-md);
                background: var(--bg-darker);
                color: var(--text-light);
                font-size: 13px;
                transition: all var(--transition-fast);
                font-family: inherit;
                box-sizing: border-box;
            }

            .interactive-input-field:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
                background: var(--bg-dark);
            }

            .interactive-input-field::placeholder {
                color: var(--text-medium);
            }

            .interactive-input-textarea {
                min-height: 80px;
                resize: vertical;
                font-family: inherit;
            }

            .interactive-input-actions {
                display: flex;
                gap: 12px;
                justify-content: flex-end;
            }

            .interactive-input-btn {
                padding: 8px 16px;
                border: 1px solid var(--border-color);
                background: var(--bg-darker);
                color: var(--text-light);
                border-radius: var(--radius-md);
                cursor: pointer;
                font-size: 13px;
                transition: all var(--transition-fast);
                min-width: 70px;
                font-family: inherit;
            }

            .interactive-input-btn:hover {
                background: var(--bg-light);
            }

            .interactive-input-btn.primary {
                background: var(--primary-color);
                border-color: var(--primary-color);
                color: white;
            }

            .interactive-input-btn.primary:hover {
                background: var(--primary-color-dark);
            }

            .interactive-input-validation {
                margin-top: 8px;
                font-size: 12px;
                color: #f44336;
                display: none;
            }

            .interactive-input-validation.show {
                display: block;
            }

            .interactive-input-field.error {
                border-color: #f44336;
                box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
            }

            .interactive-input-suggestions {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--bg-medium);
                border: 1px solid var(--border-color);
                border-top: none;
                border-radius: 0 0 var(--radius-md) var(--radius-md);
                max-height: 180px;
                overflow-y: auto;
                z-index: 1000;
                display: none;
                box-shadow: var(--shadow-md);
            }

            .interactive-input-suggestion {
                padding: 8px 14px;
                cursor: pointer;
                transition: background var(--transition-fast);
                border-bottom: 1px solid var(--border-color);
                color: var(--text-medium);
                font-size: 13px;
            }

            .interactive-input-suggestion:hover,
            .interactive-input-suggestion.selected {
                background: var(--bg-light);
                color: var(--text-light);
            }

            .interactive-input-suggestion:last-child {
                border-bottom: none;
            }

            .interactive-input-field-container {
                position: relative;
            }

            @keyframes inputShake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }

            .interactive-input-field.shake {
                animation: inputShake 0.5s ease-in-out;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * عرض حقل إدخال تفاعلي
     */
    showInput(options) {
        return new Promise((resolve) => {
            const {
                title = 'إدخال',
                label = '',
                placeholder = '',
                defaultValue = '',
                type = 'text',
                required = false,
                validation = null,
                suggestions = [],
                multiline = false,
                icon = '✏️'
            } = options;

            const id = ++this.inputId;
            const overlay = this.createInputOverlay(id, title, label, placeholder, defaultValue, type, required, validation, suggestions, multiline, icon);

            document.body.appendChild(overlay);
            this.activeInput = overlay;

            // إظهار النافذة
            requestAnimationFrame(() => {
                overlay.classList.add('show');
                const input = overlay.querySelector('.interactive-input-field');
                input.focus();
                if (defaultValue) {
                    input.select();
                }
            });

            // معالجة النتيجة
            const handleResult = (value) => {
                this.hideInput(overlay);
                resolve(value);
            };

            // حفظ مرجع للمعالج
            overlay._resultHandler = handleResult;
        });
    }

    createInputOverlay(id, title, label, placeholder, defaultValue, type, required, validation, suggestions, multiline, icon) {
        const overlay = document.createElement('div');
        overlay.className = 'interactive-input-overlay';
        overlay.dataset.id = id;

        const inputElement = multiline ? 'textarea' : 'input';
        const inputType = multiline ? '' : `type="${type}"`;

        overlay.innerHTML = `
            <div class="interactive-input-dialog">
                <div class="interactive-input-header">
                    <div class="interactive-input-icon">${icon}</div>
                    <div class="interactive-input-title">${title}</div>
                    <button class="interactive-input-close">×</button>
                </div>
                <div class="interactive-input-content">
                    ${label ? `<label class="interactive-input-label">${label}</label>` : ''}
                    <div class="interactive-input-field-container">
                        <${inputElement} 
                            class="interactive-input-field ${multiline ? 'interactive-input-textarea' : ''}" 
                            ${inputType}
                            placeholder="${placeholder}"
                            ${required ? 'required' : ''}
                        >${multiline ? defaultValue : ''}</${inputElement}>
                        ${suggestions.length > 0 ? '<div class="interactive-input-suggestions"></div>' : ''}
                    </div>
                    <div class="interactive-input-validation"></div>
                </div>
                <div class="interactive-input-actions">
                    <button class="interactive-input-btn" data-action="cancel">إلغاء</button>
                    <button class="interactive-input-btn primary" data-action="confirm">موافق</button>
                </div>
            </div>
        `;

        // تعيين القيمة الافتراضية للحقول غير متعددة الأسطر
        if (!multiline && defaultValue) {
            const input = overlay.querySelector('.interactive-input-field');
            input.value = defaultValue;
        }

        this.setupInputEvents(overlay, validation, suggestions);
        return overlay;
    }

    setupInputEvents(overlay, validation, suggestions) {
        const input = overlay.querySelector('.interactive-input-field');
        const closeBtn = overlay.querySelector('.interactive-input-close');
        const cancelBtn = overlay.querySelector('[data-action="cancel"]');
        const confirmBtn = overlay.querySelector('[data-action="confirm"]');
        const validationDiv = overlay.querySelector('.interactive-input-validation');

        // معالجة الإغلاق
        const handleCancel = () => {
            if (overlay._resultHandler) {
                overlay._resultHandler(null);
            }
        };

        // معالجة التأكيد
        const handleConfirm = () => {
            const value = input.value.trim();
            
            // التحقق من صحة البيانات
            if (validation && !validation(value)) {
                this.showValidationError(input, validationDiv, 'قيمة غير صحيحة');
                return;
            }

            if (input.required && !value) {
                this.showValidationError(input, validationDiv, 'هذا الحقل مطلوب');
                return;
            }

            if (overlay._resultHandler) {
                overlay._resultHandler(value);
            }
        };

        // مستمعي الأحداث
        closeBtn.addEventListener('click', handleCancel);
        cancelBtn.addEventListener('click', handleCancel);
        confirmBtn.addEventListener('click', handleConfirm);

        // اختصارات لوحة المفاتيح
        const keyHandler = (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
            } else if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleConfirm();
            }
        };

        input.addEventListener('keydown', keyHandler);
        overlay.addEventListener('keydown', keyHandler);

        // إغلاق عند النقر خارج النافذة
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                handleCancel();
            }
        });

        // إعداد الاقتراحات
        if (suggestions.length > 0) {
            this.setupSuggestions(input, suggestions);
        }

        // إزالة رسائل الخطأ عند الكتابة
        input.addEventListener('input', () => {
            this.hideValidationError(input, validationDiv);
        });
    }

    showValidationError(input, validationDiv, message) {
        input.classList.add('error', 'shake');
        validationDiv.textContent = message;
        validationDiv.classList.add('show');
        
        // إزالة تأثير الاهتزاز
        setTimeout(() => {
            input.classList.remove('shake');
        }, 500);
    }

    hideValidationError(input, validationDiv) {
        input.classList.remove('error');
        validationDiv.classList.remove('show');
    }

    setupSuggestions(input, suggestions) {
        const suggestionsDiv = input.parentNode.querySelector('.interactive-input-suggestions');
        let selectedIndex = -1;

        const showSuggestions = (filtered) => {
            suggestionsDiv.innerHTML = '';
            filtered.forEach((suggestion, index) => {
                const div = document.createElement('div');
                div.className = 'interactive-input-suggestion';
                div.textContent = suggestion;
                div.addEventListener('click', () => {
                    input.value = suggestion;
                    hideSuggestions();
                    input.focus();
                });
                suggestionsDiv.appendChild(div);
            });
            suggestionsDiv.style.display = filtered.length > 0 ? 'block' : 'none';
        };

        const hideSuggestions = () => {
            suggestionsDiv.style.display = 'none';
            selectedIndex = -1;
        };

        input.addEventListener('input', () => {
            const value = input.value.toLowerCase();
            const filtered = suggestions.filter(s => s.toLowerCase().includes(value));
            showSuggestions(filtered);
        });

        input.addEventListener('keydown', (e) => {
            const items = suggestionsDiv.querySelectorAll('.interactive-input-suggestion');
            
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                updateSelection(items);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                updateSelection(items);
            } else if (e.key === 'Tab' && selectedIndex >= 0) {
                e.preventDefault();
                input.value = items[selectedIndex].textContent;
                hideSuggestions();
            }
        });

        const updateSelection = (items) => {
            items.forEach((item, index) => {
                item.classList.toggle('selected', index === selectedIndex);
            });
        };

        // إخفاء الاقتراحات عند فقدان التركيز
        input.addEventListener('blur', () => {
            setTimeout(hideSuggestions, 200);
        });
    }

    hideInput(overlay) {
        overlay.classList.remove('show');
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
            if (this.activeInput === overlay) {
                this.activeInput = null;
            }
        }, 300);
    }
}

// إنشاء مثيل عام للنظام
const interactiveInputSystem = new InteractiveInputSystem();

// دوال مساعدة سريعة لاستبدال prompt
window.showPrompt = (title, label, placeholder, defaultValue) => {
    return interactiveInputSystem.showInput({
        title: title || 'إدخال',
        label: label || '',
        placeholder: placeholder || '',
        defaultValue: defaultValue || '',
        required: true
    });
};

window.showTextInput = (options) => {
    return interactiveInputSystem.showInput(options);
};
