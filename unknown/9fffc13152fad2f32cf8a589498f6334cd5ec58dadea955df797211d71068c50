/**
 * نظام الإشعارات التفاعلية المتطور
 * يوفر إشعارات منبثقة ونوافذ حوار تفاعلية
 */

class NotificationSystem {
    constructor() {
        this.notifications = new Map();
        this.notificationId = 0;
        this.containers = new Map();
        this.currentPosition = 'top-right';
        this.init();
    }

    init() {
        // إنشاء حاويات الإشعارات للمواضع المختلفة
        this.createContainers();

        // إضافة أصوات الإشعارات (اختيارية)
        this.sounds = {
            success: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            error: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            warning: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            info: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')
        };
    }

    createContainers() {
        const positions = ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'top-center', 'bottom-center'];

        positions.forEach(position => {
            const container = document.createElement('div');
            container.className = `notification-container notification-${position}`;
            container.style.display = position === this.currentPosition ? 'block' : 'none';
            document.body.appendChild(container);
            this.containers.set(position, container);
        });

        // تعيين الحاوية الافتراضية
        this.container = this.containers.get(this.currentPosition);
    }

    setPosition(position) {
        // إخفاء الحاوية الحالية
        if (this.container) {
            this.container.style.display = 'none';
        }

        // تعيين الموضع الجديد
        this.currentPosition = position;
        this.container = this.containers.get(position);

        if (this.container) {
            this.container.style.display = 'block';
        }
    }

    /**
     * عرض إشعار منبثق
     */
    showNotification(options) {
        const {
            type = 'info',
            title = '',
            message = '',
            duration = 5000,
            actions = [],
            sound = true,
            progress = false
        } = options;

        const id = ++this.notificationId;
        const notification = this.createNotificationElement(id, type, title, message, actions, progress);
        
        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // تشغيل الصوت
        if (sound && this.sounds[type]) {
            this.sounds[type].play().catch(() => {});
        }

        // إظهار الإشعار مع تأثير انتقالي
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });

        // إخفاء تلقائي
        if (duration > 0) {
            if (progress) {
                this.startProgress(notification, duration);
            }
            setTimeout(() => {
                this.hideNotification(id);
            }, duration);
        }

        return id;
    }

    /**
     * إنشاء عنصر الإشعار
     */
    createNotificationElement(id, type, title, message, actions, progress) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.dataset.id = id;

        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const titles = {
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات'
        };

        const emojis = {
            success: '🎉',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">
                    <div class="notification-icon">${icons[type]}</div>
                    ${emojis[type]} ${title || titles[type]}
                </div>
                <button class="notification-close" onclick="notificationSystem.hideNotification(${id})">×</button>
            </div>
            ${message ? `<div class="notification-content">${message}</div>` : ''}
            ${actions.length > 0 ? this.createActionsHTML(actions, id) : ''}
            ${progress ? '<div class="notification-progress" style="width: 100%"></div>' : ''}
        `;

        return notification;
    }

    /**
     * إنشاء HTML للأزرار
     */
    createActionsHTML(actions, notificationId) {
        const actionsHTML = actions.map(action => {
            const className = `notification-btn ${action.primary ? 'primary' : ''}`;
            return `<button class="${className}" onclick="notificationSystem.handleAction(${notificationId}, '${action.id}')">${action.text}</button>`;
        }).join('');

        return `<div class="notification-actions">${actionsHTML}</div>`;
    }

    /**
     * بدء شريط التقدم
     */
    startProgress(notification, duration) {
        const progressBar = notification.querySelector('.notification-progress');
        if (!progressBar) return;

        let width = 100;
        const interval = 50;
        const decrement = (100 / duration) * interval;

        const timer = setInterval(() => {
            width -= decrement;
            if (width <= 0) {
                clearInterval(timer);
                width = 0;
            }
            progressBar.style.width = width + '%';
        }, interval);
    }

    /**
     * إخفاء إشعار
     */
    hideNotification(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        notification.classList.add('hide');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 400);
    }

    /**
     * معالجة إجراءات الأزرار
     */
    handleAction(notificationId, actionId) {
        const notification = this.notifications.get(notificationId);
        if (!notification) return;

        // إرسال حدث مخصص
        const event = new CustomEvent('notificationAction', {
            detail: { notificationId, actionId }
        });
        document.dispatchEvent(event);

        // إخفاء الإشعار
        this.hideNotification(notificationId);
    }

    /**
     * عرض نافذة حوار تأكيد
     */
    showConfirm(options) {
        return new Promise((resolve) => {
            const {
                type = 'confirm',
                title = 'تأكيد',
                message = 'هل أنت متأكد؟',
                confirmText = 'تأكيد',
                cancelText = 'إلغاء',
                danger = false
            } = options;

            const overlay = this.createModalOverlay(type, title, message, [
                {
                    text: cancelText,
                    action: () => {
                        this.hideModal(overlay);
                        resolve(false);
                    }
                },
                {
                    text: confirmText,
                    primary: true,
                    danger: danger,
                    action: () => {
                        this.hideModal(overlay);
                        resolve(true);
                    }
                }
            ]);

            document.body.appendChild(overlay);
            requestAnimationFrame(() => {
                overlay.classList.add('show');
            });
        });
    }

    /**
     * عرض نافذة حوار معلومات
     */
    showAlert(options) {
        return new Promise((resolve) => {
            const {
                type = 'info',
                title = 'معلومات',
                message = '',
                buttonText = 'موافق'
            } = options;

            const overlay = this.createModalOverlay(type, title, message, [
                {
                    text: buttonText,
                    primary: true,
                    action: () => {
                        this.hideModal(overlay);
                        resolve();
                    }
                }
            ]);

            document.body.appendChild(overlay);
            requestAnimationFrame(() => {
                overlay.classList.add('show');
            });
        });
    }

    /**
     * إنشاء نافذة حوار
     */
    createModalOverlay(type, title, message, buttons) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';

        const icons = {
            confirm: '⚠',
            success: '✓',
            error: '✕',
            info: 'ℹ'
        };

        const modalEmojis = {
            confirm: '❓',
            success: '🎉',
            error: '❌',
            info: 'ℹ️'
        };

        const buttonsHTML = buttons.map(button => {
            const className = `modal-btn ${button.primary ? 'primary' : ''} ${button.danger ? 'danger' : ''}`;
            return `<button class="${className}" data-action="${button.text}">${button.text}</button>`;
        }).join('');

        overlay.innerHTML = `
            <div class="modal ${type}">
                <div class="modal-header">
                    <div class="modal-title">
                        <div class="modal-icon">${icons[type]}</div>
                        ${modalEmojis[type]} ${title}
                    </div>
                    <button class="modal-close">×</button>
                </div>
                <div class="modal-content">${message}</div>
                <div class="modal-actions">${buttonsHTML}</div>
            </div>
        `;

        // إضافة مستمعي الأحداث
        const modal = overlay.querySelector('.modal');
        const closeBtn = overlay.querySelector('.modal-close');
        const actionBtns = overlay.querySelectorAll('.modal-btn');

        closeBtn.addEventListener('click', () => {
            const cancelButton = buttons.find(b => !b.primary);
            if (cancelButton && cancelButton.action) {
                cancelButton.action();
            } else {
                this.hideModal(overlay);
            }
        });

        actionBtns.forEach((btn, index) => {
            btn.addEventListener('click', () => {
                if (buttons[index] && buttons[index].action) {
                    buttons[index].action();
                }
            });
        });

        // إغلاق عند النقر خارج النافذة
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                const cancelButton = buttons.find(b => !b.primary);
                if (cancelButton && cancelButton.action) {
                    cancelButton.action();
                } else {
                    this.hideModal(overlay);
                }
            }
        });

        // دعم اختصارات لوحة المفاتيح
        const keyHandler = (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                const cancelButton = buttons.find(b => !b.primary);
                if (cancelButton && cancelButton.action) {
                    cancelButton.action();
                } else {
                    this.hideModal(overlay);
                }
            } else if (e.key === 'Enter') {
                e.preventDefault();
                const primaryButton = buttons.find(b => b.primary);
                if (primaryButton && primaryButton.action) {
                    primaryButton.action();
                }
            }
        };

        document.addEventListener('keydown', keyHandler);

        // إزالة مستمع الأحداث عند إغلاق النافذة
        overlay._keyHandler = keyHandler;

        return overlay;
    }

    /**
     * إخفاء نافذة الحوار
     */
    hideModal(overlay) {
        overlay.classList.remove('show');

        // إزالة مستمع أحداث لوحة المفاتيح
        if (overlay._keyHandler) {
            document.removeEventListener('keydown', overlay._keyHandler);
        }

        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }

    /**
     * مسح جميع الإشعارات
     */
    clearAll() {
        this.notifications.forEach((notification, id) => {
            this.hideNotification(id);
        });
    }
}

// إنشاء مثيل عام للنظام
const notificationSystem = new NotificationSystem();

// دوال مساعدة سريعة
window.showSuccess = (message, title) => notificationSystem.showNotification({
    type: 'success',
    title: title || 'نجح',
    message: message
});

window.showError = (message, title) => notificationSystem.showNotification({
    type: 'error',
    title: title || 'خطأ',
    message: message
});

window.showWarning = (message, title) => notificationSystem.showNotification({
    type: 'warning',
    title: title || 'تحذير',
    message: message
});

window.showInfo = (message, title) => notificationSystem.showNotification({
    type: 'info',
    title: title || 'معلومات',
    message: message
});

window.showConfirm = (message, title) => notificationSystem.showConfirm({
    message: message,
    title: title || 'تأكيد'
});

window.showAlert = (message, title, type) => notificationSystem.showAlert({
    message: message,
    title: title || 'تنبيه',
    type: type || 'info'
});

// دالة لإظهار إشعار تقدم
window.showProgress = (message, title) => notificationSystem.showNotification({
    type: 'info',
    title: title || 'جاري المعالجة...',
    message: message,
    duration: 0, // لا يختفي تلقائياً
    progress: true
});

// دالة لإظهار إشعار مع أزرار مخصصة
window.showNotificationWithActions = (message, title, actions, type = 'info') => {
    return notificationSystem.showNotification({
        type: type,
        title: title,
        message: message,
        actions: actions,
        duration: 0 // لا يختفي تلقائياً مع الأزرار
    });
};

// دالة لتغيير موضع الإشعارات
window.setNotificationPosition = (position) => {
    notificationSystem.setPosition(position);
};

// دالة للحصول على المواضع المتاحة
window.getNotificationPositions = () => {
    return ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'top-center', 'bottom-center'];
};
